import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Grid,
  MenuItem,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Chip,
  Divider
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Build as BuildIcon,
  Link as LinkIcon,
  Verified as VerifiedIcon,
  Cable as CableIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';

/**
 * Componente per la creazione rapida di comande multiple
 * Ottimizzato per il workflow: cavi già selezionati -> dettagli comanda -> creazione
 */
const CreaComandaMultipla = ({ 
  open, 
  onClose, 
  onSuccess, 
  onError,
  tipoComanda,
  caviSelezionati = [],
  cantiereId 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Dati del form
  const [formData, setFormData] = useState({
    descrizione: '',
    responsabile: '',
    data_scadenza: '',
    priorita: 'NORMALE',
    note_capo_cantiere: ''
  });

  // Funzione per ottenere l'etichetta del tipo comanda
  const getTipoComandaLabel = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'Posa';
      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';
      case 'CERTIFICAZIONE': return 'Certificazione';
      default: return tipo;
    }
  };

  // Funzione per ottenere l'icona del tipo comanda
  const getTipoComandaIcon = (tipo) => {
    switch (tipo) {
      case 'POSA': return <BuildIcon />;
      case 'COLLEGAMENTO_PARTENZA': return <LinkIcon />;
      case 'COLLEGAMENTO_ARRIVO': return <LinkIcon />;
      case 'CERTIFICAZIONE': return <VerifiedIcon />;
      default: return <AssignmentIcon />;
    }
  };

  // Funzione per ottenere la descrizione del tipo comanda
  const getTipoComandaDescription = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'Comanda per la posa fisica dei cavi';
      case 'COLLEGAMENTO_PARTENZA': return 'Comanda per il collegamento lato partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Comanda per il collegamento lato arrivo';
      case 'CERTIFICAZIONE': return 'Comanda per la certificazione e test dei cavi';
      default: return 'Comanda generica';
    }
  };

  // Reset del form quando si chiude il dialog
  const handleClose = () => {
    setFormData({
      descrizione: '',
      responsabile: '',
      data_scadenza: '',
      priorita: 'NORMALE',
      note_capo_cantiere: ''
    });
    setError(null);
    onClose();
  };

  // Gestione del submit
  const handleSubmit = async () => {
    try {
      // Validazione
      if (!formData.responsabile.trim()) {
        setError('Il responsabile è obbligatorio');
        return;
      }

      if (caviSelezionati.length === 0) {
        setError('Nessun cavo selezionato');
        return;
      }

      setLoading(true);
      setError(null);

      // Prepara i dati della comanda
      const comandaData = {
        tipo_comanda: tipoComanda,
        descrizione: formData.descrizione || `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`,
        responsabile: formData.responsabile,
        data_scadenza: formData.data_scadenza || null,
        priorita: formData.priorita,
        note_capo_cantiere: formData.note_capo_cantiere
      };

      // Lista degli ID dei cavi
      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);

      console.log('Creazione comanda multipla:', {
        cantiereId,
        comandaData,
        listaIdCavi
      });

      // Crea la comanda con i cavi
      const response = await comandeService.createComandaConCavi(
        cantiereId,
        comandaData,
        listaIdCavi
      );

      console.log('Comanda creata con successo:', response);
      
      if (onSuccess) {
        onSuccess(response);
      }
      
      handleClose();
    } catch (err) {
      console.error('Errore nella creazione della comanda:', err);
      const errorMessage = err?.detail || err?.message || 'Errore nella creazione della comanda';
      setError(errorMessage);
      
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          {getTipoComandaIcon(tipoComanda)}
          Crea Comanda {getTipoComandaLabel(tipoComanda)}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          {/* Informazioni tipo comanda */}
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="subtitle2">
              {getTipoComandaDescription(tipoComanda)}
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              Verranno assegnati <strong>{caviSelezionati.length} cavi</strong> a questa comanda
            </Typography>
          </Alert>

          {/* Form dati comanda */}
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Responsabile"
                value={formData.responsabile}
                onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}
                required
                helperText="Chi eseguirà il lavoro (obbligatorio)"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Priorità"
                value={formData.priorita}
                onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}
              >
                <MenuItem value="BASSA">Bassa</MenuItem>
                <MenuItem value="NORMALE">Normale</MenuItem>
                <MenuItem value="ALTA">Alta</MenuItem>
                <MenuItem value="URGENTE">Urgente</MenuItem>
              </TextField>
            </Grid>
          </Grid>

          <TextField
            fullWidth
            label="Descrizione"
            value={formData.descrizione}
            onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
            margin="normal"
            multiline
            rows={2}
            placeholder={`Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`}
          />

          <TextField
            fullWidth
            label="Note Capo Cantiere"
            value={formData.note_capo_cantiere}
            onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}
            margin="normal"
            multiline
            rows={2}
            helperText="Istruzioni specifiche per il responsabile"
          />

          <TextField
            fullWidth
            label="Data Scadenza"
            type="date"
            value={formData.data_scadenza}
            onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}
            margin="normal"
            InputLabelProps={{ shrink: true }}
          />

          {/* Lista cavi selezionati */}
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              <CableIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Cavi Selezionati ({caviSelezionati.length})
            </Typography>
            <Box sx={{ maxHeight: 200, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <List dense>
                {caviSelezionati.map((cavo, index) => (
                  <React.Fragment key={cavo.id_cavo}>
                    <ListItem>
                      <ListItemText
                        primary={cavo.id_cavo}
                        secondary={
                          <Box>
                            <Typography variant="caption" component="span">
                              {cavo.tipologia} • {cavo.sezione} • {cavo.metri_teorici}m
                            </Typography>
                            <br />
                            <Typography variant="caption" color="text.secondary">
                              {cavo.ubicazione_partenza} → {cavo.ubicazione_arrivo}
                            </Typography>
                          </Box>
                        }
                      />
                      <Chip 
                        size="small" 
                        label={cavo.stato_installazione || 'N/A'} 
                        variant="outlined"
                      />
                    </ListItem>
                    {index < caviSelezionati.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          </Box>

          {/* Errore */}
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Annulla
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained"
          disabled={loading || !formData.responsabile.trim() || caviSelezionati.length === 0}
          startIcon={loading ? <CircularProgress size={20} /> : getTipoComandaIcon(tipoComanda)}
        >
          {loading ? 'Creazione...' : `Crea Comanda ${getTipoComandaLabel(tipoComanda)}`}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreaComandaMultipla;
