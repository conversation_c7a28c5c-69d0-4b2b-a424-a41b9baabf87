
> cms-frontend@0.1.0 start
> react-scripts start

(node:18420) [DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE] DeprecationWarning: 'onAfterSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:18420) [DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE] DeprecationWarning: 'onBeforeSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
Starting the development server...

Compiled with warnings.

[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                              no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                           no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used                    no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                           no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                          no-unused-vars
  Line 40:11:  'setOpenEliminaCavoDialog' is assigned a value but never used   no-unused-vars
  Line 40:37:  'setOpenModificaCavoDialog' is assigned a value but never used  no-unused-vars
  Line 57:10:  'homeAnchorEl' is assigned a value but never used               no-unused-vars
  Line 58:10:  'adminAnchorEl' is assigned a value but never used              no-unused-vars
  Line 59:10:  'cantieriAnchorEl' is assigned a value but never used           no-unused-vars
  Line 60:10:  'caviAnchorEl' is assigned a value but never used               no-unused-vars
  Line 69:9:   'selectedCantiereName' is assigned a value but never used       no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cantieri\CantieriFilterableTable.js
  Line 14:11:  'InfoIcon' is defined but never used                   no-unused-vars
  Line 43:10:  'filteredCantieri' is assigned a value but never used  no-unused-vars

src\components\cantieri\CreateCantiereDialog.js
  Line 21:17:  'LocationIcon' is defined but never used  no-unused-vars

src\components\cantieri\EditCantiereDialog.js
  Line 12:3:   'IconButton' is defined but never used    no-unused-vars
  Line 25:17:  'LocationIcon' is defined but never used  no-unused-vars

src\components\cantieri\HoldToViewButton.js
  Line 33:10:  'currentHoldDuration' is assigned a value but never used  no-unused-vars

src\components\cavi\CaviFilterableTable.js
  Line 2:15:   'Typography' is defined but never used                     no-unused-vars
  Line 2:64:   'Button' is defined but never used                         no-unused-vars
  Line 4:15:   'CheckBoxIcon' is defined but never used                   no-unused-vars
  Line 5:12:   'ClearIcon' is defined but never used                      no-unused-vars
  Line 6:17:   'RulerIcon' is defined but never used                      no-unused-vars
  Line 7:15:   'SettingsIcon' is defined but never used                   no-unused-vars
  Line 8:16:   'StartIcon' is defined but never used                      no-unused-vars
  Line 14:10:  'formatDate' is defined but never used                     no-unused-vars
  Line 121:9:  'handleClearSelection' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:    'Stack' is defined but never used                  no-unused-vars
  Line 11:3:   'Divider' is defined but never used                no-unused-vars
  Line 20:13:  'CancelIcon' is defined but never used             no-unused-vars
  Line 205:9:  'handleCancel' is assigned a value but never used  no-unused-vars

src\components\cavi\CertificazioneCaviImproved.js
  Line 58:15:  'DownloadIcon' is defined but never used                                                                                                    no-unused-vars
  Line 140:6:  React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array                          react-hooks/exhaustive-deps
  Line 145:6:  React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array                               react-hooks/exhaustive-deps
  Line 150:6:  React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array                     react-hooks/exhaustive-deps
  Line 155:6:  React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array                      react-hooks/exhaustive-deps
  Line 164:6:  React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:     'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:     'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:    'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 12:3:    'Dialog' is defined but never used                                                                           no-unused-vars
  Line 13:3:    'DialogTitle' is defined but never used                                                                      no-unused-vars
  Line 14:3:    'DialogContent' is defined but never used                                                                    no-unused-vars
  Line 15:3:    'DialogActions' is defined but never used                                                                    no-unused-vars
  Line 36:10:   'internalSelectedCavo' is assigned a value but never used                                                    no-unused-vars
  Line 37:10:   'openDialog' is assigned a value but never used                                                              no-unused-vars
  Line 46:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 265:23:  'latoPartenzaCollegato' is assigned a value but never used                                                   no-unused-vars
  Line 266:23:  'latoArrivoCollegato' is assigned a value but never used                                                     no-unused-vars
  Line 381:21:  'latoPartenzaCollegato' is assigned a value but never used                                                   no-unused-vars
  Line 382:21:  'latoArrivoCollegato' is assigned a value but never used                                                     no-unused-vars

src\components\cavi\ExcelPopup.js
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 28:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriDialogCompleto.js
  Line 21:3:   'ListItemText' is defined but never used              no-unused-vars
  Line 28:12:  'CloseIcon' is defined but never used                 no-unused-vars
  Line 33:10:  'getReelStateColor' is defined but never used         no-unused-vars
  Line 78:9:   'getBobinaNumber' is assigned a value but never used  no-unused-vars

src\components\cavi\ModificaBobinaDialogCompleto.js
  Line 8:3:     'Table' is defined but never used                                                                              no-unused-vars
  Line 9:3:     'TableBody' is defined but never used                                                                          no-unused-vars
  Line 10:3:    'TableCell' is defined but never used                                                                          no-unused-vars
  Line 11:3:    'TableContainer' is defined but never used                                                                     no-unused-vars
  Line 12:3:    'TableHead' is defined but never used                                                                          no-unused-vars
  Line 13:3:    'TableRow' is defined but never used                                                                           no-unused-vars
  Line 15:3:    'Alert' is defined but never used                                                                              no-unused-vars
  Line 25:3:    'Divider' is defined but never used                                                                            no-unused-vars
  Line 30:3:    'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 32:3:    'Tooltip' is defined but never used                                                                            no-unused-vars
  Line 33:3:    'Stack' is defined but never used                                                                              no-unused-vars
  Line 40:13:   'CancelIcon' is defined but never used                                                                         no-unused-vars
  Line 42:15:   'SettingsIcon' is defined but never used                                                                       no-unused-vars
  Line 50:3:    'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 51:3:    'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 52:3:    'determineCableState' is defined but never used                                                                no-unused-vars
  Line 53:3:    'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 54:3:    'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 55:3:    'isCableSpare' is defined but never used                                                                       no-unused-vars
  Line 56:3:    'isCableInstalled' is defined but never used                                                                   no-unused-vars
  Line 57:3:    'getCableStateColor' is defined but never used                                                                 no-unused-vars
  Line 58:3:    'getReelStateColor' is defined but never used                                                                  no-unused-vars
  Line 96:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 223:13:  'bobina' is assigned a value but never used                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 30:3:   'Table' is defined but never used                                                                                                                           no-unused-vars
  Line 31:3:   'TableBody' is defined but never used                                                                                                                       no-unused-vars
  Line 32:3:   'TableCell' is defined but never used                                                                                                                       no-unused-vars
  Line 33:3:   'TableContainer' is defined but never used                                                                                                                  no-unused-vars
  Line 34:3:   'TableHead' is defined but never used                                                                                                                       no-unused-vars
  Line 35:3:   'TableRow' is defined but never used                                                                                                                        no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 161:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 675:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\QuickAddCablesDialog.js
  Line 60:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 60:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 90:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 370:17:  'result' is assigned a value but never used                                                                  no-unused-vars
  Line 470:17:  'hasMetri' is assigned a value but never used                                                                no-unused-vars

src\components\cavi\SmartCaviFilter.js
  Line 196:9:   'matchesNumericTerm' is assigned a value but never used                                                               no-unused-vars
  Line 233:11:  'isNumericTerm' is assigned a value but never used                                                                    no-unused-vars
  Line 389:6:   React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioneForm.jsx
  Line 60:6:  React Hook useEffect has missing dependencies: 'loadCavi' and 'loadWeatherData'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\certificazioni\StrumentoForm.jsx
  Line 3:3:  'Paper' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 3:3:     'BarChart' is defined but never used                        no-unused-vars
  Line 4:3:     'Bar' is defined but never used                             no-unused-vars
  Line 5:3:     'XAxis' is defined but never used                           no-unused-vars
  Line 6:3:     'YAxis' is defined but never used                           no-unused-vars
  Line 7:3:     'CartesianGrid' is defined but never used                   no-unused-vars
  Line 8:3:     'Tooltip' is defined but never used                         no-unused-vars
  Line 9:3:     'Legend' is defined but never used                          no-unused-vars
  Line 10:3:    'ResponsiveContainer' is defined but never used             no-unused-vars
  Line 11:3:    'PieChart' is defined but never used                        no-unused-vars
  Line 12:3:    'Pie' is defined but never used                             no-unused-vars
  Line 13:3:    'Cell' is defined but never used                            no-unused-vars
  Line 14:3:    'ComposedChart' is defined but never used                   no-unused-vars
  Line 15:3:    'Line' is defined but never used                            no-unused-vars
  Line 16:3:    'LineChart' is defined but never used                       no-unused-vars
  Line 18:40:   'Chip' is defined but never used                            no-unused-vars
  Line 47:9:    'bobineData' is assigned a value but never used             no-unused-vars
  Line 64:9:    'totaliData' is assigned a value but never used             no-unused-vars
  Line 71:9:    'analisiData' is assigned a value but never used            no-unused-vars
  Line 79:9:    'CustomTooltip' is assigned a value but never used          no-unused-vars
  Line 95:9:    'renderCustomizedLabel' is assigned a value but never used  no-unused-vars
  Line 272:27:  'isCompleto' is assigned a value but never used             no-unused-vars
  Line 273:27:  'isInCorso' is assigned a value but never used              no-unused-vars

src\components\charts\ProgressChart.js
  Line 3:3:    'PieChart' is defined but never used                        no-unused-vars
  Line 4:3:    'Pie' is defined but never used                             no-unused-vars
  Line 5:3:    'Cell' is defined but never used                            no-unused-vars
  Line 6:3:    'BarChart' is defined but never used                        no-unused-vars
  Line 7:3:    'Bar' is defined but never used                             no-unused-vars
  Line 12:3:   'Legend' is defined but never used                          no-unused-vars
  Line 36:9:   'progressData' is assigned a value but never used           no-unused-vars
  Line 50:9:   'caviData' is assigned a value but never used               no-unused-vars
  Line 64:9:   'metricsData' is assigned a value but never used            no-unused-vars
  Line 88:9:   'CustomTooltip' is assigned a value but never used          no-unused-vars
  Line 104:9:  'renderCustomizedLabel' is assigned a value but never used  no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\comande\ComandeList.js
  Line 68:6:  React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

src\components\comande\CreaComandaConCavi.js
  Line 4:3:   'Card' is defined but never used                                                                                        no-unused-vars
  Line 5:3:   'CardContent' is defined but never used                                                                                 no-unused-vars
  Line 66:6:  React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\common\EmptyState.js
  Line 3:3:  'Box' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\LoginPageNew.js
  Line 12:3:  'CardActions' is defined but never used  no-unused-vars

src\pages\UserPage.js
  Line 16:3:  'TextField' is defined but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\GestioneComandeePage.js
  Line 4:3:    'Typography' is defined but never used                no-unused-vars
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 7:3:    'IconButton' is defined but never used                no-unused-vars
  Line 12:14:  'RefreshIcon' is defined but never used               no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 17:8:   'AdminHomeButton' is defined but never used           no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 26:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 8:3:    'Card' is defined but never used                      no-unused-vars
  Line 9:3:    'CardContent' is defined but never used               no-unused-vars
  Line 10:3:   'CardActions' is defined but never used               no-unused-vars
  Line 11:3:   'Grid' is defined but never used                      no-unused-vars
  Line 12:3:   'Divider' is defined but never used                   no-unused-vars
  Line 15:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 16:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 17:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 18:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 19:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 20:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 25:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 28:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 48:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 53:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 178:6:  React Hook useEffect has a missing dependency: 'loadStoricoBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:     'Card' is defined but never used                                                                                                                                          no-unused-vars
  Line 9:3:     'CardContent' is defined but never used                                                                                                                                   no-unused-vars
  Line 11:3:    'IconButton' is defined but never used                                                                                                                                    no-unused-vars
  Line 14:3:    'LinearProgress' is defined but never used                                                                                                                                no-unused-vars
  Line 26:8:    'InfoIcon' is defined but never used                                                                                                                                      no-unused-vars
  Line 30:15:   'ScheduleIcon' is defined but never used                                                                                                                                  no-unused-vars
  Line 32:14:   'LinkOffIcon' is defined but never used                                                                                                                                   no-unused-vars
  Line 33:15:   'TimelineIcon' is defined but never used                                                                                                                                  no-unused-vars
  Line 34:15:   'CheckBoxIcon' is defined but never used                                                                                                                                  no-unused-vars
  Line 35:27:   'CheckBoxOutlineBlankIcon' is defined but never used                                                                                                                      no-unused-vars
  Line 42:15:   'SettingsIcon' is defined but never used                                                                                                                                  no-unused-vars
  Line 50:8:    'parcoCaviService' is defined but never used                                                                                                                              no-unused-vars
  Line 51:8:    'CavoForm' is defined but never used                                                                                                                                      no-unused-vars
  Line 62:11:   'isImpersonating' is assigned a value but never used                                                                                                                      no-unused-vars
  Line 63:11:   'openEliminaCavoDialog' is assigned a value but never used                                                                                                                no-unused-vars
  Line 63:34:   'setOpenEliminaCavoDialog' is assigned a value but never used                                                                                                             no-unused-vars
  Line 63:60:   'openModificaCavoDialog' is assigned a value but never used                                                                                                               no-unused-vars
  Line 63:84:   'setOpenModificaCavoDialog' is assigned a value but never used                                                                                                            no-unused-vars
  Line 63:111:  'openAggiungiCavoDialog' is assigned a value but never used                                                                                                               no-unused-vars
  Line 63:135:  'setOpenAggiungiCavoDialog' is assigned a value but never used                                                                                                            no-unused-vars
  Line 64:9:    'navigate' is assigned a value but never used                                                                                                                             no-unused-vars
  Line 66:10:   'cantiereName' is assigned a value but never used                                                                                                                         no-unused-vars
  Line 301:19:  'setFilters' is assigned a value but never used                                                                                                                           no-unused-vars
  Line 309:10:  'statiInstallazione' is assigned a value but never used                                                                                                                   no-unused-vars
  Line 310:10:  'tipologieCavi' is assigned a value but never used                                                                                                                        no-unused-vars
  Line 310:25:  'setTipologieCavi' is assigned a value but never used                                                                                                                     no-unused-vars
  Line 670:6:   React Hook useEffect has missing dependencies: 'calculateStatistics', 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 715:9:   'getAllSelectedCavi' is assigned a value but never used                                                                                                                   no-unused-vars

src\pages\cavi\parco\CreaBobinaPage.js
  Line 4:3:   'Typography' is defined but never used                     no-unused-vars
  Line 52:9:  'handleBackToCantieri' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 4:3:   'Typography' is defined but never used                     no-unused-vars
  Line 5:3:   'Paper' is defined but never used                          no-unused-vars
  Line 26:9:  'cantiereName' is assigned a value but never used          no-unused-vars
  Line 48:9:  'handleBackToCantieri' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 4:3:   'Typography' is defined but never used                     no-unused-vars
  Line 5:3:   'Paper' is defined but never used                          no-unused-vars
  Line 26:9:  'cantiereName' is assigned a value but never used          no-unused-vars
  Line 48:9:  'handleBackToCantieri' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\VisualizzaBobinePage.js
  Line 4:3:   'Typography' is defined but never used                     no-unused-vars
  Line 37:9:  'handleBackToCantieri' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an error object to be thrown  no-throw-literal
  Line 80:11:  Expected an error object to be thrown  no-throw-literal
  Line 86:9:   Expected an error object to be thrown  no-throw-literal
  Line 89:9:   Expected an error object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an error object to be thrown       no-throw-literal
  Line 274:9:   Expected an error object to be thrown       no-throw-literal
  Line 278:9:   Expected an error object to be thrown       no-throw-literal
  Line 333:11:  Expected an error object to be thrown       no-throw-literal
  Line 435:9:   Expected an error object to be thrown       no-throw-literal
  Line 451:9:   Expected an error object to be thrown       no-throw-literal
  Line 668:9:   Expected an error object to be thrown       no-throw-literal
  Line 677:9:   Expected an error object to be thrown       no-throw-literal
  Line 681:9:   Expected an error object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an error object to be thrown       no-throw-literal
  Line 794:11:  Expected an error object to be thrown       no-throw-literal
  Line 801:9:   Expected an error object to be thrown       no-throw-literal
  Line 810:11:  Expected an error object to be thrown       no-throw-literal
  Line 817:9:   Expected an error object to be thrown       no-throw-literal
  Line 885:9:   Expected an error object to be thrown       no-throw-literal
  Line 955:3:   Duplicate key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1143:3:  Duplicate key 'getRevisioneCorrente'        no-dupe-keys
  Line 1252:9:  Expected an error object to be thrown       no-throw-literal
  Line 1282:9:  Expected an error object to be thrown       no-throw-literal
  Line 1335:9:  Expected an error object to be thrown       no-throw-literal
  Line 1382:9:  Expected an error object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'config' is defined but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an error object to be thrown          no-throw-literal
  Line 123:9:  Expected an error object to be thrown          no-throw-literal
  Line 127:9:  Expected an error object to be thrown          no-throw-literal
  Line 212:9:  Expected an error object to be thrown          no-throw-literal
  Line 226:9:  Expected an error object to be thrown          no-throw-literal
  Line 230:9:  Expected an error object to be thrown          no-throw-literal
  Line 271:9:  Expected an error object to be thrown          no-throw-literal
  Line 280:9:  Expected an error object to be thrown          no-throw-literal
  Line 284:9:  Expected an error object to be thrown          no-throw-literal
  Line 320:9:  Expected an error object to be thrown          no-throw-literal
  Line 324:9:  Expected an error object to be thrown          no-throw-literal
  Line 416:9:  Expected an error object to be thrown          no-throw-literal
  Line 425:9:  Expected an error object to be thrown          no-throw-literal
  Line 429:9:  Expected an error object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

[1m[33mWARNING[39m[22m in [1m[eslint] 
src\components\TopNavbar.js
  Line 13:3:   'Avatar' is defined but never used                              no-unused-vars
  Line 20:25:  'AdminIcon' is defined but never used                           no-unused-vars
  Line 21:19:  'ConstructionIcon' is defined but never used                    no-unused-vars
  Line 22:12:  'CableIcon' is defined but never used                           no-unused-vars
  Line 23:18:  'ReportIcon' is defined but never used                          no-unused-vars
  Line 40:11:  'setOpenEliminaCavoDialog' is assigned a value but never used   no-unused-vars
  Line 40:37:  'setOpenModificaCavoDialog' is assigned a value but never used  no-unused-vars
  Line 57:10:  'homeAnchorEl' is assigned a value but never used               no-unused-vars
  Line 58:10:  'adminAnchorEl' is assigned a value but never used              no-unused-vars
  Line 59:10:  'cantieriAnchorEl' is assigned a value but never used           no-unused-vars
  Line 60:10:  'caviAnchorEl' is assigned a value but never used               no-unused-vars
  Line 69:9:   'selectedCantiereName' is assigned a value but never used       no-unused-vars

src\components\admin\UserExpirationChecker.js
  Line 11:10:  'lastCheck' is assigned a value but never used  no-unused-vars

src\components\cantieri\CantieriFilterableTable.js
  Line 14:11:  'InfoIcon' is defined but never used                   no-unused-vars
  Line 43:10:  'filteredCantieri' is assigned a value but never used  no-unused-vars

src\components\cantieri\CreateCantiereDialog.js
  Line 21:17:  'LocationIcon' is defined but never used  no-unused-vars

src\components\cantieri\EditCantiereDialog.js
  Line 12:3:   'IconButton' is defined but never used    no-unused-vars
  Line 25:17:  'LocationIcon' is defined but never used  no-unused-vars

src\components\cantieri\HoldToViewButton.js
  Line 33:10:  'currentHoldDuration' is assigned a value but never used  no-unused-vars

src\components\cavi\CaviFilterableTable.js
  Line 2:15:   'Typography' is defined but never used                     no-unused-vars
  Line 2:64:   'Button' is defined but never used                         no-unused-vars
  Line 4:15:   'CheckBoxIcon' is defined but never used                   no-unused-vars
  Line 5:12:   'ClearIcon' is defined but never used                      no-unused-vars
  Line 6:17:   'RulerIcon' is defined but never used                      no-unused-vars
  Line 7:15:   'SettingsIcon' is defined but never used                   no-unused-vars
  Line 8:16:   'StartIcon' is defined but never used                      no-unused-vars
  Line 14:10:  'formatDate' is defined but never used                     no-unused-vars
  Line 121:9:  'handleClearSelection' is assigned a value but never used  no-unused-vars

src\components\cavi\CavoForm.js
  Line 6:3:    'Stack' is defined but never used                  no-unused-vars
  Line 11:3:   'Divider' is defined but never used                no-unused-vars
  Line 20:13:  'CancelIcon' is defined but never used             no-unused-vars
  Line 205:9:  'handleCancel' is assigned a value but never used  no-unused-vars

src\components\cavi\CertificazioneCaviImproved.js
  Line 58:15:  'DownloadIcon' is defined but never used                                                                                                    no-unused-vars
  Line 140:6:  React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array                          react-hooks/exhaustive-deps
  Line 145:6:  React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array                               react-hooks/exhaustive-deps
  Line 150:6:  React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array                     react-hooks/exhaustive-deps
  Line 155:6:  React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array                      react-hooks/exhaustive-deps
  Line 164:6:  React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

src\components\cavi\CollegamentiCavo.js
  Line 8:3:     'List' is defined but never used                                                                             no-unused-vars
  Line 9:3:     'ListItem' is defined but never used                                                                         no-unused-vars
  Line 10:3:    'ListItemText' is defined but never used                                                                     no-unused-vars
  Line 12:3:    'Dialog' is defined but never used                                                                           no-unused-vars
  Line 13:3:    'DialogTitle' is defined but never used                                                                      no-unused-vars
  Line 14:3:    'DialogContent' is defined but never used                                                                    no-unused-vars
  Line 15:3:    'DialogActions' is defined but never used                                                                    no-unused-vars
  Line 36:10:   'internalSelectedCavo' is assigned a value but never used                                                    no-unused-vars
  Line 37:10:   'openDialog' is assigned a value but never used                                                              no-unused-vars
  Line 46:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 265:23:  'latoPartenzaCollegato' is assigned a value but never used                                                   no-unused-vars
  Line 266:23:  'latoArrivoCollegato' is assigned a value but never used                                                     no-unused-vars
  Line 381:21:  'latoPartenzaCollegato' is assigned a value but never used                                                   no-unused-vars
  Line 382:21:  'latoArrivoCollegato' is assigned a value but never used                                                     no-unused-vars

src\components\cavi\ExcelPopup.js
  Line 14:3:   'Divider' is defined but never used            no-unused-vars
  Line 28:10:  'filePath' is assigned a value but never used  no-unused-vars

src\components\cavi\IncompatibleReelDialog.js
  Line 17:3:  'Alert' is defined but never used  no-unused-vars

src\components\cavi\InserisciMetriDialogCompleto.js
  Line 21:3:   'ListItemText' is defined but never used              no-unused-vars
  Line 28:12:  'CloseIcon' is defined but never used                 no-unused-vars
  Line 33:10:  'getReelStateColor' is defined but never used         no-unused-vars
  Line 78:9:   'getBobinaNumber' is assigned a value but never used  no-unused-vars

src\components\cavi\ModificaBobinaDialogCompleto.js
  Line 8:3:     'Table' is defined but never used                                                                              no-unused-vars
  Line 9:3:     'TableBody' is defined but never used                                                                          no-unused-vars
  Line 10:3:    'TableCell' is defined but never used                                                                          no-unused-vars
  Line 11:3:    'TableContainer' is defined but never used                                                                     no-unused-vars
  Line 12:3:    'TableHead' is defined but never used                                                                          no-unused-vars
  Line 13:3:    'TableRow' is defined but never used                                                                           no-unused-vars
  Line 15:3:    'Alert' is defined but never used                                                                              no-unused-vars
  Line 25:3:    'Divider' is defined but never used                                                                            no-unused-vars
  Line 30:3:    'ListItemText' is defined but never used                                                                       no-unused-vars
  Line 32:3:    'Tooltip' is defined but never used                                                                            no-unused-vars
  Line 33:3:    'Stack' is defined but never used                                                                              no-unused-vars
  Line 40:13:   'CancelIcon' is defined but never used                                                                         no-unused-vars
  Line 42:15:   'SettingsIcon' is defined but never used                                                                       no-unused-vars
  Line 50:3:    'CABLE_STATES' is defined but never used                                                                       no-unused-vars
  Line 51:3:    'REEL_STATES' is defined but never used                                                                        no-unused-vars
  Line 52:3:    'determineCableState' is defined but never used                                                                no-unused-vars
  Line 53:3:    'determineReelState' is defined but never used                                                                 no-unused-vars
  Line 54:3:    'canModifyCable' is defined but never used                                                                     no-unused-vars
  Line 55:3:    'isCableSpare' is defined but never used                                                                       no-unused-vars
  Line 56:3:    'isCableInstalled' is defined but never used                                                                   no-unused-vars
  Line 57:3:    'getCableStateColor' is defined but never used                                                                 no-unused-vars
  Line 58:3:    'getReelStateColor' is defined but never used                                                                  no-unused-vars
  Line 96:6:    React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 223:13:  'bobina' is assigned a value but never used                                                                    no-unused-vars

src\components\cavi\ParcoCavi.js
  Line 8:3:    'Card' is defined but never used                                                                                                                            no-unused-vars
  Line 9:3:    'CardContent' is defined but never used                                                                                                                     no-unused-vars
  Line 10:3:   'CardActions' is defined but never used                                                                                                                     no-unused-vars
  Line 23:3:   'ListItemIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 24:3:   'ListItemButton' is defined but never used                                                                                                                  no-unused-vars
  Line 25:3:   'Divider' is defined but never used                                                                                                                         no-unused-vars
  Line 29:3:   'IconButton' is defined but never used                                                                                                                      no-unused-vars
  Line 30:3:   'Table' is defined but never used                                                                                                                           no-unused-vars
  Line 31:3:   'TableBody' is defined but never used                                                                                                                       no-unused-vars
  Line 32:3:   'TableCell' is defined but never used                                                                                                                       no-unused-vars
  Line 33:3:   'TableContainer' is defined but never used                                                                                                                  no-unused-vars
  Line 34:3:   'TableHead' is defined but never used                                                                                                                       no-unused-vars
  Line 35:3:   'TableRow' is defined but never used                                                                                                                        no-unused-vars
  Line 39:11:  'EditIcon' is defined but never used                                                                                                                        no-unused-vars
  Line 43:15:  'ViewListIcon' is defined but never used                                                                                                                    no-unused-vars
  Line 44:14:  'WarningIcon' is defined but never used                                                                                                                     no-unused-vars
  Line 50:69:  'isEmpty' is defined but never used                                                                                                                         no-unused-vars
  Line 79:10:  'isFirstInsertion' is assigned a value but never used                                                                                                       no-unused-vars
  Line 161:6:  React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 675:9:  'renderBobineCards' is assigned a value but never used                                                                                                      no-unused-vars

src\components\cavi\QuickAddCablesDialog.js
  Line 60:10:   'loading' is assigned a value but never used                                                                 no-unused-vars
  Line 60:19:   'setLoading' is assigned a value but never used                                                              no-unused-vars
  Line 90:6:    React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  Line 370:17:  'result' is assigned a value but never used                                                                  no-unused-vars
  Line 470:17:  'hasMetri' is assigned a value but never used                                                                no-unused-vars

src\components\cavi\SmartCaviFilter.js
  Line 196:9:   'matchesNumericTerm' is assigned a value but never used                                                               no-unused-vars
  Line 233:11:  'isNumericTerm' is assigned a value but never used                                                                    no-unused-vars
  Line 389:6:   React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioneForm.jsx
  Line 60:6:  React Hook useEffect has missing dependencies: 'loadCavi' and 'loadWeatherData'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

src\components\certificazioni\CertificazioniList.jsx
  Line 25:13:  'DownloadIcon' is defined but never used  no-unused-vars

src\components\certificazioni\StrumentoForm.jsx
  Line 3:3:  'Paper' is defined but never used  no-unused-vars

src\components\charts\BoqChart.js
  Line 3:3:     'BarChart' is defined but never used                        no-unused-vars
  Line 4:3:     'Bar' is defined but never used                             no-unused-vars
  Line 5:3:     'XAxis' is defined but never used                           no-unused-vars
  Line 6:3:     'YAxis' is defined but never used                           no-unused-vars
  Line 7:3:     'CartesianGrid' is defined but never used                   no-unused-vars
  Line 8:3:     'Tooltip' is defined but never used                         no-unused-vars
  Line 9:3:     'Legend' is defined but never used                          no-unused-vars
  Line 10:3:    'ResponsiveContainer' is defined but never used             no-unused-vars
  Line 11:3:    'PieChart' is defined but never used                        no-unused-vars
  Line 12:3:    'Pie' is defined but never used                             no-unused-vars
  Line 13:3:    'Cell' is defined but never used                            no-unused-vars
  Line 14:3:    'ComposedChart' is defined but never used                   no-unused-vars
  Line 15:3:    'Line' is defined but never used                            no-unused-vars
  Line 16:3:    'LineChart' is defined but never used                       no-unused-vars
  Line 18:40:   'Chip' is defined but never used                            no-unused-vars
  Line 47:9:    'bobineData' is assigned a value but never used             no-unused-vars
  Line 64:9:    'totaliData' is assigned a value but never used             no-unused-vars
  Line 71:9:    'analisiData' is assigned a value but never used            no-unused-vars
  Line 79:9:    'CustomTooltip' is assigned a value but never used          no-unused-vars
  Line 95:9:    'renderCustomizedLabel' is assigned a value but never used  no-unused-vars
  Line 272:27:  'isCompleto' is assigned a value but never used             no-unused-vars
  Line 273:27:  'isInCorso' is assigned a value but never used              no-unused-vars

src\components\charts\ProgressChart.js
  Line 3:3:    'PieChart' is defined but never used                        no-unused-vars
  Line 4:3:    'Pie' is defined but never used                             no-unused-vars
  Line 5:3:    'Cell' is defined but never used                            no-unused-vars
  Line 6:3:    'BarChart' is defined but never used                        no-unused-vars
  Line 7:3:    'Bar' is defined but never used                             no-unused-vars
  Line 12:3:   'Legend' is defined but never used                          no-unused-vars
  Line 36:9:   'progressData' is assigned a value but never used           no-unused-vars
  Line 50:9:   'caviData' is assigned a value but never used               no-unused-vars
  Line 64:9:   'metricsData' is assigned a value but never used            no-unused-vars
  Line 88:9:   'CustomTooltip' is assigned a value but never used          no-unused-vars
  Line 104:9:  'renderCustomizedLabel' is assigned a value but never used  no-unused-vars

src\components\charts\TimelineChart.js
  Line 3:3:  'LineChart' is defined but never used  no-unused-vars

src\components\comande\ComandeList.js
  Line 68:6:  React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

src\components\comande\CreaComandaConCavi.js
  Line 4:3:   'Card' is defined but never used                                                                                        no-unused-vars
  Line 5:3:   'CardContent' is defined but never used                                                                                 no-unused-vars
  Line 66:6:  React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\components\common\EmptyState.js
  Line 3:3:  'Box' is defined but never used  no-unused-vars

src\components\common\ExcelLikeFilter.js
  Line 16:3:  'MenuItem' is defined but never used    no-unused-vars
  Line 17:3:  'Select' is defined but never used      no-unused-vars
  Line 19:3:  'InputLabel' is defined but never used  no-unused-vars

src\pages\CaviPage.js
  Line 1:8:  'React' is defined but never used  no-unused-vars

src\pages\CertificazioniPageDebug.jsx
  Line 49:19:  'useAuth' is assigned a value but never used  no-unused-vars

src\pages\HomePage.js
  Line 2:27:  'Grid' is defined but never used                 no-unused-vars
  Line 2:33:  'Card' is defined but never used                 no-unused-vars
  Line 2:39:  'CardContent' is defined but never used          no-unused-vars
  Line 2:52:  'CardActionArea' is defined but never used       no-unused-vars
  Line 2:68:  'Avatar' is defined but never used               no-unused-vars
  Line 5:25:  'AdminIcon' is defined but never used            no-unused-vars
  Line 6:19:  'ConstructionIcon' is defined but never used     no-unused-vars
  Line 7:12:  'CableIcon' is defined but never used            no-unused-vars
  Line 8:18:  'ReportIcon' is defined but never used           no-unused-vars
  Line 43:9:  'navigateTo' is assigned a value but never used  no-unused-vars

src\pages\LoginPageNew.js
  Line 12:3:  'CardActions' is defined but never used  no-unused-vars

src\pages\UserPage.js
  Line 16:3:  'TextField' is defined but never used  no-unused-vars

src\pages\cantieri\CantierePage.js
  Line 24:11:  'isImpersonating' is assigned a value but never used                                                               no-unused-vars
  Line 53:6:   React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\GestioneComandeePage.js
  Line 4:3:    'Typography' is defined but never used                no-unused-vars
  Line 5:3:    'Paper' is defined but never used                     no-unused-vars
  Line 7:3:    'IconButton' is defined but never used                no-unused-vars
  Line 12:14:  'RefreshIcon' is defined but never used               no-unused-vars
  Line 13:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 17:8:   'AdminHomeButton' is defined but never used           no-unused-vars
  Line 21:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 26:9:   'cantiereName' is assigned a value but never used     no-unused-vars

src\pages\cavi\ParcoCaviPage.js
  Line 8:3:    'Card' is defined but never used                      no-unused-vars
  Line 9:3:    'CardContent' is defined but never used               no-unused-vars
  Line 10:3:   'CardActions' is defined but never used               no-unused-vars
  Line 11:3:   'Grid' is defined but never used                      no-unused-vars
  Line 12:3:   'Divider' is defined but never used                   no-unused-vars
  Line 15:11:  'HomeIcon' is defined but never used                  no-unused-vars
  Line 16:15:  'ViewListIcon' is defined but never used              no-unused-vars
  Line 17:10:  'AddIcon' is defined but never used                   no-unused-vars
  Line 18:11:  'EditIcon' is defined but never used                  no-unused-vars
  Line 19:13:  'DeleteIcon' is defined but never used                no-unused-vars
  Line 20:14:  'HistoryIcon' is defined but never used               no-unused-vars
  Line 25:8:   'ParcoCavi' is defined but never used                 no-unused-vars
  Line 28:11:  'isImpersonating' is assigned a value but never used  no-unused-vars
  Line 48:9:   'handleSuccess' is assigned a value but never used    no-unused-vars
  Line 53:9:   'handleError' is assigned a value but never used      no-unused-vars

src\pages\cavi\ReportCaviPageNew.js
  Line 178:6:  React Hook useEffect has a missing dependency: 'loadStoricoBobine'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src\pages\cavi\TestCaviPage.js
  Line 1:27:  'useEffect' is defined but never used  no-unused-vars

src\pages\cavi\VisualizzaCaviPage.js
  Line 8:3:     'Card' is defined but never used                                                                                                                                          no-unused-vars
  Line 9:3:     'CardContent' is defined but never used                                                                                                                                   no-unused-vars
  Line 11:3:    'IconButton' is defined but never used                                                                                                                                    no-unused-vars
  Line 14:3:    'LinearProgress' is defined but never used                                                                                                                                no-unused-vars
  Line 26:8:    'InfoIcon' is defined but never used                                                                                                                                      no-unused-vars
  Line 30:15:   'ScheduleIcon' is defined but never used                                                                                                                                  no-unused-vars
  Line 32:14:   'LinkOffIcon' is defined but never used                                                                                                                                   no-unused-vars
  Line 33:15:   'TimelineIcon' is defined but never used                                                                                                                                  no-unused-vars
  Line 34:15:   'CheckBoxIcon' is defined but never used                                                                                                                                  no-unused-vars
  Line 35:27:   'CheckBoxOutlineBlankIcon' is defined but never used                                                                                                                      no-unused-vars
  Line 42:15:   'SettingsIcon' is defined but never used                                                                                                                                  no-unused-vars
  Line 50:8:    'parcoCaviService' is defined but never used                                                                                                                              no-unused-vars
  Line 51:8:    'CavoForm' is defined but never used                                                                                                                                      no-unused-vars
  Line 62:11:   'isImpersonating' is assigned a value but never used                                                                                                                      no-unused-vars
  Line 63:11:   'openEliminaCavoDialog' is assigned a value but never used                                                                                                                no-unused-vars
  Line 63:34:   'setOpenEliminaCavoDialog' is assigned a value but never used                                                                                                             no-unused-vars
  Line 63:60:   'openModificaCavoDialog' is assigned a value but never used                                                                                                               no-unused-vars
  Line 63:84:   'setOpenModificaCavoDialog' is assigned a value but never used                                                                                                            no-unused-vars
  Line 63:111:  'openAggiungiCavoDialog' is assigned a value but never used                                                                                                               no-unused-vars
  Line 63:135:  'setOpenAggiungiCavoDialog' is assigned a value but never used                                                                                                            no-unused-vars
  Line 64:9:    'navigate' is assigned a value but never used                                                                                                                             no-unused-vars
  Line 66:10:   'cantiereName' is assigned a value but never used                                                                                                                         no-unused-vars
  Line 301:19:  'setFilters' is assigned a value but never used                                                                                                                           no-unused-vars
  Line 309:10:  'statiInstallazione' is assigned a value but never used                                                                                                                   no-unused-vars
  Line 310:10:  'tipologieCavi' is assigned a value but never used                                                                                                                        no-unused-vars
  Line 310:25:  'setTipologieCavi' is assigned a value but never used                                                                                                                     no-unused-vars
  Line 670:6:   React Hook useEffect has missing dependencies: 'calculateStatistics', 'caviAttivi', 'caviSpare', '[1m[31merror[39m[22m[1m', and 'user'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  Line 715:9:   'getAllSelectedCavi' is assigned a value but never used                                                                                                                   no-unused-vars

src\pages\cavi\parco\CreaBobinaPage.js
  Line 4:3:   'Typography' is defined but never used                     no-unused-vars
  Line 52:9:  'handleBackToCantieri' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\EliminaBobinaPage.js
  Line 4:3:   'Typography' is defined but never used                     no-unused-vars
  Line 5:3:   'Paper' is defined but never used                          no-unused-vars
  Line 26:9:  'cantiereName' is assigned a value but never used          no-unused-vars
  Line 48:9:  'handleBackToCantieri' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\ModificaBobinaPage.js
  Line 4:3:   'Typography' is defined but never used                     no-unused-vars
  Line 5:3:   'Paper' is defined but never used                          no-unused-vars
  Line 26:9:  'cantiereName' is assigned a value but never used          no-unused-vars
  Line 48:9:  'handleBackToCantieri' is assigned a value but never used  no-unused-vars

src\pages\cavi\parco\VisualizzaBobinePage.js
  Line 4:3:   'Typography' is defined but never used                     no-unused-vars
  Line 37:9:  'handleBackToCantieri' is assigned a value but never used  no-unused-vars

src\services\adminService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\authService.js
  Line 78:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 80:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 86:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal
  Line 89:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown  no-throw-literal

src\services\cantieriService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\caviService.js
  Line 260:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 274:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 278:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 333:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 435:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 451:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 668:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 677:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 681:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 755:17:  'token' is assigned a value but never used  no-unused-vars
  Line 775:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 794:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 801:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 810:11:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 817:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 885:9:   Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 955:3:   [1m[31mDuplicate[39m[22m[1m key 'updateCavoForCompatibility'  no-dupe-keys
  Line 1143:3:  [1m[31mDuplicate[39m[22m[1m key 'getRevisioneCorrente'        no-dupe-keys
  Line 1252:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1282:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1335:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal
  Line 1382:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown       no-throw-literal

src\services\certificazioneService.js
  Line 1:8:  'config' is defined but never used  no-unused-vars

src\services\comandeService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\parcoCaviService.js
  Line 1:8:    'axios' is defined but never used              no-unused-vars
  Line 5:7:    'API_URL' is assigned a value but never used   no-unused-vars
  Line 83:13:  'sentData' is assigned a value but never used  no-unused-vars
  Line 109:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 123:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 127:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 212:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 226:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 230:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 271:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 280:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 284:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 320:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 324:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 416:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 425:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal
  Line 429:9:  Expected an [1m[31merror[39m[22m[1m object to be thrown          no-throw-literal

src\services\reportService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

src\services\userService.js
  Line 1:8:  'axios' is defined but never used             no-unused-vars
  Line 5:7:  'API_URL' is assigned a value but never used  no-unused-vars

[39m[22m

webpack compiled with [1m[33m1 warning[39m[22m
^C